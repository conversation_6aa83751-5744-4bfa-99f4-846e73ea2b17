{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "chillerlan/php-qrcode": "^5.0", "guzzlehttp/guzzle": "^7.4", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.6", "livewire/livewire": "^3.5", "maatwebsite/excel": "^3.1", "propa/tcpdi": "^1.3", "spatie/laravel-permission": "^6.9"}, "require-dev": {"spatie/laravel-ignition": "^2.0", "fakerphp/faker": "^1.17", "laravel/sail": "^1.12", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^10.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}