<?php

use App\Http\Controllers\Api\ParticipantController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('updateInscrieri', 'App\Http\Controllers\VisitorController@updateInscrieri');

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Participant Registration API with API Key Authentication
Route::middleware('api.key')->group(function () {
    // Register a new participant
    Route::post('participants/register', [ParticipantController::class, 'register']);

    // Get participant information
    Route::get('participants/get', [ParticipantController::class, 'getParticipant']);

    // API status endpoint
    Route::get('status', function () {
        return response()->json([
            'success' => true,
            'message' => 'Eventrium API is running',
            'version' => '1.0',
            'timestamp' => now()->toISOString()
        ]);
    });
});
