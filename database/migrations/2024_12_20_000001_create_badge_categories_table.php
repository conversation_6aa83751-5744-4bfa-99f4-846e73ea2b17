<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('badge_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // internal name (participant, staff, etc.)
            $table->string('display_name'); // display name (Participant, Staff, etc.)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('badge_categories');
    }
};
