<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('badge_fields', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pdf_template_id')->constrained('pdf_templates')->onDelete('cascade');
            $table->string('field_name'); // name, company, region, etc.
            $table->string('json_key')->nullable(); // key from visitor's date JSON (null for built-in fields)
            $table->integer('x_coordinate')->default(0);
            $table->integer('y_coordinate');
            $table->integer('font_size')->default(12);
            $table->integer('max_width')->default(0); // 0 = full width
            $table->string('alignment')->default('C'); // L, C, R
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('badge_fields');
    }
};
