<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vizitatori', function (Blueprint $table) {
            $table->id();
			$table->string('cod')->unique();
			$table->string('nume');
			$table->string('prenume');
			$table->string('email')->nullable();
			$table->string('categorie')->nullable();
			$table->integer('badge_index')->default(0);
			$table->string('regiune')->nullable();
			$table->json('date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vizitatori');
    }
};
