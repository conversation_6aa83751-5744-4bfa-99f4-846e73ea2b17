<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pdf_templates', function (Blueprint $table) {
            $table->dropColumn(['qr_x_coordinate', 'qr_y_coordinate', 'qr_size']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pdf_templates', function (Blueprint $table) {
            $table->integer('qr_x_coordinate')->default(0);
            $table->integer('qr_y_coordinate')->default(85);
            $table->integer('qr_size')->default(30);
        });
    }
};
