<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expozanti', function (Blueprint $table) {
            $table->id();
			$table->integer('id_user');
			$table->string('firma');
			$table->string('nume')->nullable();
			$table->string('prenume')->nullable();
			$table->string('email');
			$table->string('telefon')->nullable();
			$table->string('cui')->nullable();
			$table->string('adresa')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expozanti');
    }
};
