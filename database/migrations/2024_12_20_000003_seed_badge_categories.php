<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('badge_categories')->insert([
            [
                'name' => 'participant',
                'display_name' => 'Participant',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'speaker',
                'display_name' => 'Speaker',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'invited_speaker',
                'display_name' => 'Invited Speaker',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'staff',
                'display_name' => 'Staff',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('badge_categories')->whereIn('name', [
            'participant', 'speaker', 'invited_speaker', 'staff'
        ])->delete();
    }
};
