<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pdf_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('badge_categories')->onDelete('cascade');
            $table->string('filename'); // stored filename
            $table->string('original_name'); // original uploaded filename
            $table->integer('qr_x_coordinate')->default(0); // X coordinate for QR code (0 = centered)
            $table->integer('qr_y_coordinate')->default(85); // Y coordinate for QR code
            $table->integer('qr_size')->default(30); // QR code size
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pdf_templates');
    }
};
