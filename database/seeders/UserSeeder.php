<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('users')->insert([
            'id' => 1,
            'name' => 'Digital',
            'email' => '<EMAIL>',
            'password' => Hash::make('deni83!@%'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        DB::table('users')->insert([
            'id' => 2,
            'name' => 'STAFF',
            'email' => '<EMAIL>',
            'password' => Hash::make('eventscan2024'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
