# Eventrium Participant Registration API

## Overview
This API allows external applications to register participants in the Eventrium platform. It uses simple API key authentication for security.

## Authentication
All API requests require an API key to be included in the request headers or as a query parameter.

### API Key Setup
1. Add your API key to the `.env` file:
   ```
   EVENTRIUM_API_KEY=your_secure_api_key_here
   ```

2. Include the API key in requests using one of these methods:
   - **Header**: `X-API-Key: your_api_key_here`
   - **Query Parameter**: `?api_key=your_api_key_here`

## Base URL
```
https://your-eventrium-domain.com/api
```

## Endpoints

### 1. Register Participant
**POST** `/participants/register`

Register a new participant in the system.

#### Request Body
```json
{
    "nume": "<PERSON>scu",
    "prenume": "Ion",
    "email": "<EMAIL>",
    "categorie": "participant",
    "regiune": "<PERSON>ucuresti",
    "badge_index": 1,
    "extra_fields": {
        "company": "ABC Company",
        "position": "Manager",
        "phone": "+40123456789"
    }
}
```

#### Required Fields
- `nume` (string, max 255): Last name
- `prenume` (string, max 255): First name

#### Optional Fields
- `email` (string, email): Email address (auto-generated if not provided)
- `categorie` (string): Category (default: "participant")
- `regiune` (string): Region
- `badge_index` (integer): Badge index number
- `extra_fields` (object): Additional custom fields

#### Success Response (201)
```json
{
    "success": true,
    "message": "Participant registered successfully",
    "data": {
        "id": 123,
        "code": "abc123def456ghi789",
        "nume": "Popescu",
        "prenume": "Ion",
        "email": "<EMAIL>",
        "categorie": "participant",
        "regiune": "Bucuresti",
        "badge_index": 1,
        "created_at": "2024-01-15T10:30:00.000000Z"
    }
}
```

#### Error Responses
- **409 Conflict**: Email already exists
- **422 Validation Error**: Invalid input data
- **401 Unauthorized**: Invalid API key
- **500 Internal Error**: Server error

### 2. Get Participant
**GET** `/participants/get`

Retrieve participant information by code or email.

#### Query Parameters
- `code` (string): Participant code
- `email` (string): Participant email

At least one parameter is required.

#### Success Response (200)
```json
{
    "success": true,
    "data": {
        "id": 123,
        "code": "abc123def456ghi789",
        "nume": "Popescu",
        "prenume": "Ion",
        "email": "<EMAIL>",
        "categorie": "participant",
        "regiune": "Bucuresti",
        "badge_index": 1,
        "extra_data": {
            "company": "ABC Company",
            "position": "Manager"
        },
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z"
    }
}
```

### 3. API Status
**GET** `/status`

Check if the API is running.

#### Success Response (200)
```json
{
    "success": true,
    "message": "Eventrium API is running",
    "version": "1.0",
    "timestamp": "2024-01-15T10:30:00.000000Z"
}
```

## Error Handling
All error responses follow this format:
```json
{
    "success": false,
    "message": "Error description",
    "error": "ERROR_CODE",
    "errors": {} // Validation errors (if applicable)
}
```

## Rate Limiting
Currently no rate limiting is implemented, but it's recommended to implement reasonable request limits in production.

## Security Notes
1. Keep your API key secure and don't expose it in client-side code
2. Use HTTPS in production
3. Consider implementing IP whitelisting for additional security
4. Regularly rotate your API keys

## Testing
You can test the API using tools like Postman, curl, or the provided PHP examples in the controller comments.

### Example curl request:
```bash
curl -X POST https://your-domain.com/api/participants/register \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key_here" \
  -d '{
    "nume": "Test",
    "prenume": "User",
    "email": "<EMAIL>",
    "categorie": "participant"
  }'
```
