<?php

namespace App\Livewire;

use App\Http\Controllers\MagicLinkController;
use App\Models\Expozant;
use App\Models\MagicLink;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Livewire\Component;
use App\Models\User;
use Livewire\WithPagination;

class Expozanti extends Component {
	use WithPagination;

	public $id_user;
	public $expozant = [
		'nume'    => '',
		'prenume' => '',
		'firma'   => '',
		'email'   => '',
		'telefon' => '',
		'cui'     => '',
		'adresa'  => '',
	];
	public $isOpen   = false;

	public function render() {
		return view('livewire.expozanti', [
			'expozanti' => User::role('expozant')->with('expozant')->paginate(10)
		]);
	}

	public function create() {
		$this->resetInputFields();
		$this->openModal();
	}

	public function openModal() {
		$this->isOpen = true;
	}

	public function closeModal() {
		$this->isOpen = false;
		$this->resetInputFields();
	}

	private function resetInputFields() {
		$this->id_user  = '';
		$this->expozant = [
			'nume'    => '',
			'prenume' => '',
			'firma'   => '',
			'email'   => '',
			'telefon' => '',
			'cui'     => '',
			'adresa'  => '',
		];
	}

	public function store() {
		$this->validate([
			'expozant.nume'    => 'required',
			'expozant.prenume' => 'required',
			'expozant.firma'   => 'required',
			'expozant.email'   => 'required|email|unique:users,email,' . $this->id_user,
		]);

		$user = User::find($this->id_user);
		if (!$user) {
			$user               = new User();
			$pass               = Str::random(8);
			$user->password     = Hash::make($pass);
			$user->raw_password = $pass;
			$user->email        = $this->expozant['email'];
			$user->name         = $this->expozant['nume'] . ' ' . $this->expozant['prenume'];
			$user->save();
		}
		$this->id_user = $user->id;
		$user->assignRole('expozant');

		// update information from expozanti table
		$expozant = Expozant::where('id_user', $this->id_user)->first();
		if (!$expozant) {
			$expozant          = new Expozant();
			$expozant->id_user = $this->id_user;
		}
		$expozant->firma   = $this->expozant['firma'];
		$expozant->nume    = $this->expozant['nume'];
		$expozant->prenume = $this->expozant['prenume'];
		$expozant->email   = $this->expozant['email'];
		$expozant->telefon = $this->expozant['telefon'];
		$expozant->cui     = $this->expozant['cui'];
		$expozant->adresa  = $this->expozant['adresa'];
		$expozant->save();

		session()->flash('message', $this->id_user ? 'Expozantul a fost actualizat.' : 'Expozantul a fost creat.');

		$this->closeModal();
		$this->resetInputFields();
	}

	public function edit($id) {
		$user           = User::findOrFail($id);
		$this->id_user  = $id;
		$expozant       = $user->expozant;
		$this->expozant = [
			'nume'    => $expozant->nume,
			'prenume' => $expozant->prenume,
			'firma'   => $expozant->firma,
			'email'   => $user->email,
			'telefon' => $expozant->telefon,
			'cui'     => $expozant->cui,
			'adresa'  => $expozant->adresa,
		];

		$this->openModal();
	}

	public function delete($id) {
		Expozant::where('id_user', $id)->delete();
		MagicLink::where('user_id', $id)->delete();
		$user = User::find($id);
		$user->removeRole('expozant');
		$user->delete();
		session()->flash('message', 'Expozanul a fost șters.');
	}

	public function sendEmail($id) {
		$controller = new MagicLinkController();
		try {
			$controller->create($id);
			session()->flash('message', 'Emailul cu datele și link-ul de conectare a fost trimis cu succes.');
		} catch (\Exception $e) {
			session()->flash('message', 'Eroare la trimiterea emailului: ' . $e->getMessage());
		}
	}
}
