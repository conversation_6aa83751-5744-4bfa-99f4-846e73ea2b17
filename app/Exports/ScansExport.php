<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ScansExport implements FromCollection, WithStyles, ShouldAutoSize {
	public function __construct(protected $scanari) {
	}

	/**
	 * @return \Illuminate\Support\Collection
	 */
	public function collection() {
		return $this->scanari;
	}

	public function styles(Worksheet $sheet) {
		return [
			1 => ['font' => ['bold' => true]],
		];
	}

}
