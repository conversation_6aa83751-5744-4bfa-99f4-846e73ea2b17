<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BadgeField extends Model
{
    use HasFactory;

    protected $fillable = [
        'pdf_template_id',
        'field_name',
        'json_key',
        'x_coordinate',
        'y_coordinate',
        'font_size',
        'max_width',
        'alignment',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the PDF template that owns this field.
     */
    public function pdfTemplate()
    {
        return $this->belongsTo(PdfTemplate::class, 'pdf_template_id');
    }

    /**
     * Get the value for this field from a visitor.
     */
    public function getValueFromVisitor($visitor)
    {
        switch ($this->field_name) {
            case 'name':
                return mb_strtoupper(trim($visitor->prenume) . ' ' . trim($visitor->nume));
            case 'first_name':
                return mb_strtoupper(trim($visitor->prenume));
            case 'last_name':
                return mb_strtoupper(trim($visitor->nume));
			case 'index_badge_reversed':
			case 'index_badge':
				return mb_strtoupper($visitor->badge_index ?? '');
			case 'region':
                return mb_strtoupper($visitor->regiune ?? '');
            case 'category':
                $category = BadgeCategory::where('name', $visitor->categorie)->first();
                return $category ? mb_strtoupper($category->display_name) : mb_strtoupper($visitor->categorie);
            case 'qr_code':
                // QR code fields should never render text - they're handled separately as images
                return '';
            default:
                // Custom field from JSON
				if (strpos($this->json_key, 'extra_') !== 0) {
					$this->json_key = 'extra_' . $this->json_key;
				}
                if ($this->json_key && $visitor->date && isset($visitor->date->{$this->json_key})) {
					return mb_strtoupper($visitor->date->{$this->json_key});
				}
				return '';
        }
    }

    /**
     * Get available built-in field options.
     */
    public static function getBuiltInFields()
    {
        return [
            'name' => 'Nume complet',
			'first_name' => 'Prenume',
			'last_name' => 'Nume',
			'index_badge' => 'Index badge',
			'index_badge_reversed' => 'Index badge (inversat)',
			'region' => 'Regiune',
			'category' => 'Categorie',
			'qr_code' => 'Cod QR',
        ];
    }
}
