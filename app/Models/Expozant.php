<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Expozant extends Model {
	use HasFactory;

	protected $fillable = [
		'id_user',
		'firma',
		'nume',
		'prenume',
		'email',
		'telefon',
		'cui',
		'adresa',
	];

	protected $table = 'expozanti';

	public function user() {
		return $this->belongsTo(User::class, 'id', 'id_user');
	}
}
