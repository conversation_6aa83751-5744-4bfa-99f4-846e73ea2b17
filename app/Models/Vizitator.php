<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vizitator extends Model {
	use HasFactory;

	// set db connection

	protected $table = 'vizitatori';

	protected $fillable = [
		'cod',
		'nume',
		'prenume',
		'email',
		'categorie',
		'badge_index',
		'regiune',
		'date'
	];

	protected function date(): Attribute {
		return Attribute::make(function ($value) {
			return json_decode($value);
		});
	}

//	protected $connection = 'formular';
//
//	protected $table    = 'users';
//	protected $fillable = [
//		'cod',
//		'tip',
//		'email',
//		'nume',
//		'prenume',
//		'password',
//		'password_clear',
//		'cnp',
//		'cuim',
//		'telefon',
//		'tara',
//		'judet',
//		'oras',
//		'adresa_corespondenta',
//		'angajat_la',
//		'adresa_institutiei',
//		'link_website',
//		'link_cv',
//		'id_emitator_puncte',
//		'grad_profesional',
//		'grad_didactic',
//		'specializare',
//		'alta_specializare',
//		'is_membru',
//		'cont_activ',
//		'cere_completare',
//		'tip_participare',
//		'sursa_user',
//		'platitor_cazare',
//	];


}
