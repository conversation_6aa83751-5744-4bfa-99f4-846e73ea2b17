<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VisitorExtraField extends Model
{
    use HasFactory;

    protected $table = 'visitors_extra_fields';

    protected $fillable = [
        'field_key',
        'field_label',
        'field_type',
        'field_options',
        'is_required',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'field_options' => 'array',
        'is_required' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get all active extra fields ordered by sort_order
     */
    public static function getActiveFields()
    {
        return self::where('is_active', true)->orderBy('sort_order')->get();
    }

    /**
     * Get field options as array
     */
    public function getOptionsArray()
    {
        if ($this->field_type === 'select' && $this->field_options) {
            return $this->field_options;
        }
        return [];
    }

    /**
     * Get the validation rule for this field
     */
    public function getValidationRule()
    {
        $rules = [];
        
        if ($this->is_required) {
            $rules[] = 'required';
        } else {
            $rules[] = 'nullable';
        }

        switch ($this->field_type) {
            case 'email':
                $rules[] = 'email';
                break;
            case 'phone':
                $rules[] = 'string';
                $rules[] = 'max:20';
                break;
            case 'url':
                $rules[] = 'url';
                break;
            case 'select':
                if (!empty($this->field_options)) {
                    $rules[] = 'in:' . implode(',', $this->field_options);
                }
                break;
            case 'text':
            default:
                $rules[] = 'string';
                $rules[] = 'max:255';
                break;
        }

        return implode('|', $rules);
    }

    /**
     * Get the HTML input type for this field
     */
    public function getInputType()
    {
        switch ($this->field_type) {
            case 'email':
                return 'email';
            case 'phone':
                return 'tel';
            case 'url':
                return 'url';
            case 'number':
                return 'number';
            case 'date':
                return 'date';
            case 'text':
            default:
                return 'text';
        }
    }

    /**
     * Check if this field should be rendered as a select dropdown
     */
    public function isSelectField()
    {
        return $this->field_type === 'select' && !empty($this->field_options);
    }

    /**
     * Scope to get only active fields
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get fields ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get field by key
     */
    public static function getByKey($key)
    {
        return self::where('field_key', $key)->first();
    }

    /**
     * Create or update a field
     */
    public static function createOrUpdateField($data)
    {
        return self::updateOrCreate(
            ['field_key' => $data['field_key']],
            $data
        );
    }
}
