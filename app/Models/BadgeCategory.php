<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BadgeCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
    ];

    /**
     * Get the PDF template for this category.
     */
    public function pdfTemplate()
    {
        return $this->hasOne(PdfTemplate::class, 'category_id');
    }

    /**
     * Get all visitors for this category.
     */
    public function visitors()
    {
        return $this->hasMany(Vizitator::class, 'categorie', 'name');
    }
}
