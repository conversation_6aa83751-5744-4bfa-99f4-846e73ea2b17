<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScanHistory extends Model
{
    use HasFactory;

	protected $table = 'scan_history';

	protected $fillable = [
		'id_vizitator',
		'id_user',
	];

	public function vizitator() {
		return $this->belongsTo(Vizitator::class, 'id_vizitator', 'id');
	}

	public function user() {
		return $this->belongsTo(User::class, 'id_user', 'id');
	}
}
