<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PdfTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'filename',
        'original_name',
    ];

    /**
     * Get the badge category that owns this template.
     */
    public function category()
    {
        return $this->belongsTo(BadgeCategory::class, 'category_id');
    }

    /**
     * Get the full path to the PDF file.
     */
    public function getFullPathAttribute()
    {
        return public_path('assets/pdf_templates/' . $this->filename);
    }

    /**
     * Get the badge fields for this template.
     */
    public function badgeFields()
    {
        return $this->hasMany(BadgeField::class, 'pdf_template_id')->orderBy('sort_order');
    }

    /**
     * Get the QR code field configuration.
     */
    public function qrField()
    {
        return $this->badgeFields()->where('field_name', 'qr_code')->first();
    }

    /**
     * Check if the PDF file exists.
     */
    public function fileExists()
    {
        return file_exists($this->full_path);
    }
}
