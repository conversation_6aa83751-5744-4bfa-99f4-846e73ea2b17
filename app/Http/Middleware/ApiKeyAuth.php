<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ApiKeyAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $apiKey = $request->header('X-API-Key') ?? $request->input('api_key');
        
        // Define your API keys here or store them in config/database
        $validApiKeys = [
            env('EVENTRIUM_API_KEY', 'eventrium_2024_api_key_default'),
            // Add more API keys as needed
        ];
        
        if (!$apiKey || !in_array($apiKey, $validApiKeys)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or missing API key',
                'error' => 'UNAUTHORIZED'
            ], 401);
        }
        
        return $next($request);
    }
}
