<?php

namespace App\Http\Controllers;

use App\Models\MagicLink;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;

class MagicLinkController extends Controller {
	public function create($id) {
		$user = User::findOrFail($id);

		$magicLink = MagicLink::create([
			'user_id'    => $user->id,
			'token'      => Str::random(32),
			'expires_at' => now()->addHours(24),
		]);

		$url = url("/login/magic/{$magicLink->token}");

		Mail::send('emails.magic-link', ['user' => $user, 'magic_link' => $url], function ($message) use ($user) {
			$message->to($user->email)->subject('Date de conectare - Eventrium Scan');
		});

		return response()->json(['message' => 'Emailul cu datele și link-ul de conectare a fost trimis cu succes.']);
	}

	public function login($token) {
		try {
			$magicLink = MagicLink::where('token', $token)->where('expires_at', '>', now())->whereNull('used_at')->firstOrFail();
		} catch (\Exception $e) {
			return redirect('/login')->with('error', 'Link-ul de conectare a expirat sau a fost deja folosit.');
		}

		$magicLink->update(['used_at' => now()]);

//		auth()->login($magicLink->user);
		if (Auth::attempt(['email' => $magicLink->user->email, 'password' => $magicLink->user->raw_password], true)) {
			session()->regenerate();
			return redirect('dashboard')->with(['success' => 'Sunteți conectat.']);
		} else {
			return back()->withErrors(['email' => 'Email sau parolă incorectă.']);
		}

		return redirect()->intended('/dashboard');
	}
}