<?php

namespace App\Http\Controllers;

use App\Models\BadgeCategory;
use Illuminate\Http\Request;

class BadgeCategoryController extends Controller
{
    /**
     * Display a listing of badge categories.
     */
    public function index()
    {
        $categories = BadgeCategory::with('pdfTemplate')->get();
        return view('badge-categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new badge category.
     */
    public function create()
    {
        return view('badge-categories.create');
    }

    /**
     * Store a newly created badge category in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:badge_categories,name',
            'display_name' => 'required|string|max:255',
        ]);

        BadgeCategory::create([
            'name' => $request->name,
            'display_name' => $request->display_name,
        ]);

        return redirect()->route('badge-categories.index')
            ->with('success', 'Categoria de badge a fost creată cu succes.');
    }

    /**
     * Show the form for editing the specified badge category.
     */
    public function edit(BadgeCategory $badgeCategory)
    {
        return view('badge-categories.edit', compact('badgeCategory'));
    }

    /**
     * Update the specified badge category in storage.
     */
    public function update(Request $request, BadgeCategory $badgeCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:badge_categories,name,' . $badgeCategory->id,
            'display_name' => 'required|string|max:255',
        ]);

        $badgeCategory->update([
            'name' => $request->name,
            'display_name' => $request->display_name,
        ]);

        return redirect()->route('badge-categories.index')
            ->with('success', 'Categoria de badge a fost actualizată cu succes.');
    }

    /**
     * Remove the specified badge category from storage.
     */
    public function destroy(BadgeCategory $badgeCategory)
    {
        // Check if category has visitors
        if ($badgeCategory->visitors()->count() > 0) {
            return redirect()->route('badge-categories.index')
                ->with('error', 'Nu se poate șterge categoria deoarece există participanți asociați.');
        }

        $badgeCategory->delete();

        return redirect()->route('badge-categories.index')
            ->with('success', 'Categoria de badge a fost ștearsă cu succes.');
    }
}
