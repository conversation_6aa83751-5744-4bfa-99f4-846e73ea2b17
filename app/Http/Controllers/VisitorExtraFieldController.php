<?php

namespace App\Http\Controllers;

use App\Models\VisitorExtraField;
use Illuminate\Http\Request;

class VisitorExtraFieldController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $fields = VisitorExtraField::ordered()->get();
        return view('visitor-extra-fields.index', compact('fields'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('visitor-extra-fields.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'field_key' => 'required|string|max:255|unique:visitors_extra_fields,field_key',
            'field_label' => 'required|string|max:255',
            'field_type' => 'required|in:text,email,phone,url,number,date,select',
            'field_options' => 'nullable|array',
            'field_options.*' => 'string|max:255',
            'is_required' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        VisitorExtraField::create([
            'field_key' => $request->field_key,
            'field_label' => $request->field_label,
            'field_type' => $request->field_type,
            'field_options' => $request->field_type === 'select' ? $request->field_options : null,
            'is_required' => $request->has('is_required'),
            'is_active' => $request->has('is_active'),
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('visitor-extra-fields.index')
            ->with('success', 'Câmpul a fost creat cu succes.');
    }

    /**
     * Display the specified resource.
     */
    public function show(VisitorExtraField $visitorExtraField)
    {
        return view('visitor-extra-fields.show', compact('visitorExtraField'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(VisitorExtraField $visitorExtraField)
    {
        return view('visitor-extra-fields.edit', compact('visitorExtraField'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, VisitorExtraField $visitorExtraField)
    {
        $request->validate([
            'field_key' => 'required|string|max:255|unique:visitors_extra_fields,field_key,' . $visitorExtraField->id,
            'field_label' => 'required|string|max:255',
            'field_type' => 'required|in:text,email,phone,url,number,date,select',
            'field_options' => 'nullable|array',
            'field_options.*' => 'string|max:255',
            'is_required' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $visitorExtraField->update([
            'field_key' => $request->field_key,
            'field_label' => $request->field_label,
            'field_type' => $request->field_type,
            'field_options' => $request->field_type === 'select' ? $request->field_options : null,
            'is_required' => $request->has('is_required'),
            'is_active' => $request->has('is_active'),
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('visitor-extra-fields.index')
            ->with('success', 'Câmpul a fost actualizat cu succes.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(VisitorExtraField $visitorExtraField)
    {
        $visitorExtraField->delete();

        return redirect()->route('visitor-extra-fields.index')
            ->with('success', 'Câmpul a fost șters cu succes.');
    }
}
