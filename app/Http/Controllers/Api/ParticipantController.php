<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Vizitator;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class ParticipantController extends Controller
{
    /**
     * Register a new participant via API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        try {
            // Validate the incoming request
            $validatedData = $request->validate([
                'nume' => 'required|string|max:255',
                'prenume' => 'required|string|max:255',
                'email' => 'nullable|email|max:255',
                'categorie' => 'nullable|string|max:100',
                'regiune' => 'nullable|string|max:255',
                'badge_index' => 'nullable|integer',
                'extra_fields' => 'nullable|array', // Additional JSON fields
            ]);

            // Generate email if not provided
            if (empty($validatedData['email'])) {
                $validatedData['email'] = "noemail_" . Str::random(6) . '@example.com';
            }

            // Check if participant already exists (by email)
            $existingParticipant = Vizitator::where('email', $validatedData['email'])->first();
            if ($existingParticipant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Participant with this email already exists',
                    'error' => 'DUPLICATE_EMAIL',
                    'participant_id' => $existingParticipant->id,
                    'participant_code' => $existingParticipant->cod
                ], 409);
            }

            // Prepare the data for storage
            $participantData = [
                'cod' => Str::random(20),
                'nume' => $validatedData['nume'],
                'prenume' => $validatedData['prenume'],
                'email' => $validatedData['email'],
                'categorie' => $validatedData['categorie'] ?? 'participant',
                'regiune' => $validatedData['regiune'] ?? null,
                'badge_index' => $validatedData['badge_index'] ?? 0,
            ];

            // Prepare JSON data (combine all request data for flexibility)
            $jsonData = array_merge($request->all(), $validatedData['extra_fields'] ?? []);
            
            // Remove sensitive data from JSON storage
            unset($jsonData['api_key']);
            
            $participantData['date'] = json_encode($jsonData, JSON_PRETTY_PRINT);

            // Create the participant
            $participant = Vizitator::create($participantData);

            return response()->json([
                'success' => true,
                'message' => 'Participant registered successfully',
                'data' => [
                    'id' => $participant->id,
                    'code' => $participant->cod,
                    'nume' => $participant->nume,
                    'prenume' => $participant->prenume,
                    'email' => $participant->email,
                    'categorie' => $participant->categorie,
                    'regiune' => $participant->regiune,
                    'badge_index' => $participant->badge_index,
                    'created_at' => $participant->created_at->toISOString(),
                ]
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'error' => 'VALIDATION_ERROR',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while registering the participant',
                'error' => 'INTERNAL_ERROR',
                'details' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get participant information by code or email
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getParticipant(Request $request)
    {
        try {
            $request->validate([
                'code' => 'nullable|string',
                'email' => 'nullable|email',
            ]);

            $code = $request->input('code');
            $email = $request->input('email');

            if (!$code && !$email) {
                return response()->json([
                    'success' => false,
                    'message' => 'Either code or email parameter is required',
                    'error' => 'MISSING_PARAMETER'
                ], 400);
            }

            $query = Vizitator::query();
            
            if ($code) {
                $query->where('cod', $code);
            } elseif ($email) {
                $query->where('email', $email);
            }

            $participant = $query->first();

            if (!$participant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Participant not found',
                    'error' => 'NOT_FOUND'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $participant->id,
                    'code' => $participant->cod,
                    'nume' => $participant->nume,
                    'prenume' => $participant->prenume,
                    'email' => $participant->email,
                    'categorie' => $participant->categorie,
                    'regiune' => $participant->regiune,
                    'badge_index' => $participant->badge_index,
                    'extra_data' => $participant->date ? json_decode($participant->date, true) : null,
                    'created_at' => $participant->created_at->toISOString(),
                    'updated_at' => $participant->updated_at->toISOString(),
                ]
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'error' => 'VALIDATION_ERROR',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the participant',
                'error' => 'INTERNAL_ERROR',
                'details' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}

/*
=== VANILLA PHP USAGE EXAMPLE ===

// Example 1: Register a new participant
<?php
$apiUrl = 'https://your-eventrium-domain.com/api/participants/register';
$apiKey = 'your_api_key_here';

$participantData = [
    'nume' => 'Popescu',
    'prenume' => 'Ion',
    'email' => '<EMAIL>',
    'categorie' => 'participant',
    'regiune' => 'Bucuresti',
    'badge_index' => 1,
    'extra_fields' => [
        'company' => 'ABC Company',
        'position' => 'Manager',
        'phone' => '+40123456789'
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($participantData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'X-API-Key: ' . $apiKey
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$result = json_decode($response, true);

if ($httpCode === 201 && $result['success']) {
    echo "Participant registered successfully!\n";
    echo "ID: " . $result['data']['id'] . "\n";
    echo "Code: " . $result['data']['code'] . "\n";
} else {
    echo "Error: " . $result['message'] . "\n";
    if (isset($result['errors'])) {
        print_r($result['errors']);
    }
}

// Example 2: Get participant by email
$getUrl = 'https://your-eventrium-domain.com/api/participants/get?email=<EMAIL>';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $getUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'X-API-Key: ' . $apiKey
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$result = json_decode($response, true);

if ($httpCode === 200 && $result['success']) {
    echo "Participant found!\n";
    print_r($result['data']);
} else {
    echo "Participant not found or error occurred.\n";
}
?>

=== JAVASCRIPT/FETCH EXAMPLE ===

// Register participant
const registerParticipant = async (participantData) => {
    try {
        const response = await fetch('https://your-eventrium-domain.com/api/participants/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': 'your_api_key_here'
            },
            body: JSON.stringify(participantData)
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
            console.log('Participant registered:', result.data);
            return result.data;
        } else {
            console.error('Registration failed:', result.message);
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
};

// Usage
registerParticipant({
    nume: 'Ionescu',
    prenume: 'Maria',
    email: '<EMAIL>',
    categorie: 'speaker',
    regiune: 'Cluj',
    extra_fields: {
        company: 'Tech Corp',
        position: 'CTO'
    }
});

*/
