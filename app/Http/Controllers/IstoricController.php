<?php

namespace App\Http\Controllers;

use App\Exports\ScansExport;
use App\Models\ScanHistory;
use App\Models\Vizitator;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class IstoricController extends Controller {
	public function index() {
		if (auth()->user()->hasRole('superadmin') || auth()->user()->hasRole('admin')) {
//			$istoric = ScanHistory::select('id_vizitator', DB::raw('count(*) as count_scanari'))->groupBy('id_vizitator')->get();
			$istoric = ScanHistory::select('id_vizitator', DB::raw('MIN(created_at) as first_scan'), DB::raw('count(*) as count_scanari'))->groupBy('id_vizitator')->orderBy('first_scan', 'desc')->get();
		} else {
//			$istoric = ScanHistory::select('id_vizitator', DB::raw('count(*) as count_scanari'))->where('id_expozant', '=', auth()->user()->id)->groupBy('id_vizitator')->get();
			$istoric = ScanHistory::select('id_vizitator', DB::raw('MIN(created_at) as first_scan'), DB::raw('count(*) as count_scanari'))->where('id_expozant', '=', auth()->user()->id)->groupBy('id_vizitator')->orderBy('first_scan', 'desc')->get();
		}
		return view('istoric', compact('istoric'));
	}

	public function export(Request $request) {
		$scanari = new Collection();
		if (auth()->user()->hasRole('superadmin') || auth()->user()->hasRole('admin')) {
			if ($request->input('expozant')) {
				$istoric = ScanHistory::select('id_vizitator', DB::raw('count(*) as count_scanari'), DB::raw('MIN(created_at) as first_scan'))->where('id_expozant', '=', $request->input('expozant'))->groupBy('id_vizitator')->orderBy('first_scan', 'desc')->get();
			} else {
				$istoric = ScanHistory::select('id_vizitator', DB::raw('count(*) as count_scanari'), DB::raw('MIN(created_at) as first_scan'))->groupBy('id_vizitator')->orderBy('first_scan', 'desc')->get();
			}
		} else {
			$istoric = ScanHistory::select('id_vizitator', DB::raw('count(*) as count_scanari'), DB::raw('MIN(created_at) as first_scan'))->where('id_expozant', '=', auth()->user()->id)->groupBy('id_vizitator')->orderBy('first_scan', 'desc')->get();
		}

		$scanari->push([
			'Nr',
			'Tip',
			'Nume',
			'Prenume',
			'Email',
			'Telefon',
			'Functie',
			'Companie',
			'Telefon companie',
			'Judet companie',
			'Oras companie',
			'Adresa companie',
			'Numar scanari',
			'Data primului scan',
		]);

		$i = 1;
		foreach ($istoric as $scan) {
			$scanari->push([
				'Nr'                 => $i++,
				'Tip'                => ucfirst($scan->vizitator->categorie),
				'Nume'               => $scan->vizitator->nume,
				'Prenume'            => $scan->vizitator->prenume,
				'Email'              => $scan->vizitator->email,
				'Telefon'            => $scan->vizitator->telefon,
				'Functie'            => $scan->vizitator->functie,
				'Companie'           => @$scan->vizitator->date->extra_companie,
				'Telefon companie'   => @$scan->vizitator->date->companie_telefon,
				'Judet companie'     => @$scan->vizitator->date->companie_judet,
				'Oras companie'      => @$scan->vizitator->date->companie_oras,
				'Adresa companie'    => @$scan->vizitator->date->companie_adresa,
				'Numar scanari'      => $scan->count_scanari,
				'Data primului scan' => $scan->first_scan,
			]);
		}

//		dd($scanari);
//		$scanari->downloadExcel('scanari.xlsx');
//		Excel::download($scanari, 'scanari.xlsx');
		return Excel::download(new ScansExport($scanari), 'scanari.xlsx');
	}

}
