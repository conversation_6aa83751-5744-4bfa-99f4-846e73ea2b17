@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header bg-gradient-primary">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="text-white">Template-uri PDF</h4>
                            <a href="{{ route('pdf-templates.create') }}" class="btn btn-light">
                                <i class="fas fa-plus me-1"></i>Adaugă template
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Categorie</th>
                                        <th><PERSON>șier original</th>
                                        <th>Câmpuri</th>
                                        <th>Status fișier</th>
                                        <th>Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($templates as $template)
                                        <tr>
                                            <td>{{ $template->id }}</td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    {{ $template->category->display_name }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ url('assets/pdf_templates/' . $template->filename) }}" target="_blank" class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-file-pdf me-1"></i>
                                                    Vezi fișier
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    {{ $template->badgeFields()->count() }} câmpuri
                                                </span>
                                            </td>
                                            <td>
                                                @if($template->fileExists())
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>Disponibil
                                                    </span>
                                                @else
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-times me-1"></i>Lipsește
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('badge-fields.index', $template) }}"
                                                       class="btn btn btn-outline-info"
                                                       title="Gestionează câmpuri">
                                                        <i class="fas fa-list"></i>
                                                    </a>
                                                    <a href="{{ route('pdf-templates.test-badge', $template) }}"
                                                       class="btn btn btn-outline-success"
                                                       title="Test badge"
                                                       target="_blank">
                                                        <i class="fas fa-vial"></i>
                                                    </a>
                                                    <a href="{{ route('pdf-templates.edit', $template) }}"
                                                       class="btn btn btn-outline-primary"
                                                       title="Editează">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('pdf-templates.destroy', $template) }}"
                                                          method="POST"
                                                          style="display: inline;"
                                                          onsubmit="return confirm('Sigur doriți să ștergeți acest template?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn btn-outline-danger"
                                                                title="Șterge">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="7" class="text-center">Nu există template-uri PDF.</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
