@extends('layouts.user_type.auth')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header bg-gradient-primary">
                    <h4 class="text-white">Istoric scanări</h4>
                    <a href="{{ route('istoric.export') }}" target="_blank" class="btn btn-success">Exportă scanările</a>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if ($istoric->count())
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="visitors">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Nume</th>
                                        <th>Tip</th>
                                        <th>Ora scanare</th>
                                        <th>Total scanări</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($istoric as $visitor)
                                        <tr>
                                            <td>{{ $loop->iteration }}</td>
                                            <td>{{ $visitor->vizitator->nume . ' ' . $visitor->vizitator->prenume }}</td>
                                            <td>{{ ucfirst($visitor->vizitator->tip) }}</td>
                                            <td>{{ date('d.m.Y H:i', strtotime($visitor->first_scan)) }}</td>
                                            <td>{{ $visitor->count_scanari }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-center">No visitors found.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('scripts')
    <script>
        $(document).ready(function() {
            $('#visitors').DataTable();
        });
    </script>
@endsection