@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="card">
            <div class="card-header bg-gradient-primary">
                <h4 class="text-white">Editează categorie badge</h4>
            </div>
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <form action="{{ route('badge-categories.update', $badgeCategory) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="mb-3">
                        <label for="name" class="form-label">Nume intern
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $badgeCategory->name) }}" placeholder="ex: vip_guest" required>
                        <div class="form-text">
                            Numele intern folosit în cod (doar litere mici, cifre și underscore)
                        </div>
                        @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-3">
                        <label for="display_name" class="form-label">Nume afișat
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control @error('display_name') is-invalid @enderror" id="display_name" name="display_name" value="{{ old('display_name', $badgeCategory->display_name) }}" placeholder="ex: VIP Guest" required>
                        <div class="form-text">
                            Numele care va fi afișat în interfață
                        </div>
                        @error('display_name')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('badge-categories.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Înapoi
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Actualizează
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
