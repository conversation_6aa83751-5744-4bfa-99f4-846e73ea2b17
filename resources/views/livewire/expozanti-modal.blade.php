<div class="modal " tabindex="-1" role="dialog" style="display: block;">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $id_user ? 'Actualizează Expozant' : 'Adaugă Expozant' }}</h5>
                <button type="button" class="btn-close text-dark" data-dismiss="modal" aria-label="Close" wire:click="closeModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <label for="firma">Companie</label>
                        <input type="text" class="form-control" id="firma" wire:model="expozant.firma">
                        @error('firma') <span class="text-danger">{{ $message }}</span>@enderror
                    </div>
                    <div class="form-group">
                        <label for="nume">Nume</label>
                        <input type="text" class="form-control" id="nume" wire:model="expozant.nume">
                        @error('nume') <span class="text-danger">{{ $message }}</span>@enderror
                    </div>
                    <div class="form-group">
                        <label for="prenume">Prenume</label>
                        <input type="text" class="form-control" id="prenume" wire:model="expozant.prenume">
                        @error('prenume') <span class="text-danger">{{ $message }}</span>@enderror
                    </div>
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" class="form-control" id="email" wire:model="expozant.email">
                        @error('email') <span class="text-danger">{{ $message }}</span>@enderror
                    </div>
                    <div class="form-group">
                        <label for="telefon">Telefon</label>
                        <input type="text" class="form-control" id="telefon" wire:model="expozant.telefon">
                    </div>
                    <div class="form-group">
                        <label for="cui">CUI</label>
                        <input type="text" class="form-control" id="cui" wire:model="expozant.cui">
                    </div>
                    <div class="form-group">
                        <label for="adresa">Adresa</label>
                        <input type="text" class="form-control" id="adresa" wire:model="expozant.adresa">
                    </div>
                </form>
                @if ($errors->any())
                    <div class="alert alert-danger text-white">
                        <ul class="m-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" wire:click.prevent="store()" class="btn btn-primary">Salvează</button>
                <button type="button" class="btn btn-secondary" wire:click="closeModal()">Închide</button>
            </div>
        </div>
    </div>
</div>