<div>
    @if (session()->has('message'))
        <div class="alert alert-success">
            {{ session('message') }}
        </div>
    @endif
    <button wire:click="create()" class="btn btn-primary">Adaugă expozant</button>
    @if($isOpen)
        @include('livewire.expozanti-modal')
    @endif
    <div class="card">
        <div class="table-responsive">
            <table class="table align-items-center mb-0">
                <thead>
                <tr>
                    <th class="text-uppercase text-secondary text-xs font-weight-bolder opacity-7">Id</th>
                    <th class="text-uppercase text-secondary text-xs font-weight-bolder opacity-7 ps-2">Companie</th>
                    <th class="text-center text-uppercase text-secondary text-xs font-weight-bolder opacity-7">Nume</th>
                    <th class="text-center text-uppercase text-secondary text-xs font-weight-bolder opacity-7">Email</th>
                    <th class="text-secondary opacity-7"></th>
                </tr>
                </thead>
                <tbody>
                @foreach($expozanti as $user)
                    <tr>
                        <td>
                            <p class="text-sm font-weight-bold mb-0 px-3">{{ $user->id }}</p>
                        </td>
                        <td>
                            <p class="text-sm font-weight-bold mb-0 px-3">{{ $user->expozant->firma }}</p>
                        </td>
                        <td>
                            <p class="text-sm font-weight-bold mb-0 px-3">{{ $user->expozant->nume . ' ' . $user->expozant->prenume }}</p>
                        </td>
                        <td>
                            <p class="text-sm font-weight-bold mb-0 px-3">{{ $user->email }}</p>
                        </td>
                        <td class="align-middle">
                            <div class="text-end">
                                <a href="{{ route('istoric.export', ['expozant' => $user->id]) }}" target="_blank" class="btn btn-success btn-sm m-0">Exportă scanările</a>
                                <button wire:click="sendEmail({{ $user->id }})" class="btn btn-warning btn-sm m-0">Magic link</button>
                            </div>
                            <div class="mt-1 text-end">
                                <button wire:click="edit({{ $user->id }})" class="btn btn-primary btn-sm m-0">Editare</button>
                                <button wire:click="delete({{ $user->id }})" class="btn btn-danger btn-sm m-0">Șterge</button>
                            </div>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    </div>
    {{ $expozanti->links() }}
</div>