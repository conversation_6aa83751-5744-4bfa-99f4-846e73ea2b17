@extends('layouts.user_type.auth')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient-primary">
                    <h4 class="text-white">Actualizați participant</h4>
                </div>
                <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                    <form action="{{ route('visitors.update', $visitor->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row info-personale">
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="nume" placeholder="Nume" value="{{ old('nume', $visitor->nume) }}">
                                @error('nume') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="prenume" placeholder="Prenume" value="{{ old('prenume', $visitor->prenume) }}">
                                @error('prenume') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="email" name="email" placeholder="Email" value="{{ old('email', $visitor->email) }}">
                                @error('email') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="telefon" placeholder="Telefon" value="{{ old('telefon', $visitor->date->telefon ?? '') }}">
                                @error('telefon') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="adresa_corespondenta" placeholder="Adresă" value="{{ old('adresa_corespondenta', $visitor->date->adresa_corespondenta ?? '') }}">
                                @error('adresa') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="regiune" placeholder="Regiune" value="{{ old('regiune', $visitor->regiune ?? '') }}">
                                @error('regiune') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>

                            <!-- Dynamic Extra Fields - Only show fields with values -->
                            @php
                                $visitorData = (array)$visitor->date;
                                $availableFields = \App\Models\VisitorExtraField::getActiveFields();
                            @endphp

                            @foreach($availableFields as $field)
                                @php
                                    $currentValue = old('extra_' . $field->field_key, $visitorData['extra_' . $field->field_key] ?? '');
                                @endphp
                                @if(!empty($currentValue))
                                    <div class="col-md-4 mt-3" data-field-key="{{ $field->field_key }}">
                                        @if($field->field_type === 'select')
                                            <div class="input-group">
                                                <select class="form-control" name="extra_{{ $field->field_key }}" {{ $field->is_required ? 'required' : '' }}>
                                                    <option value="">{{ $field->field_label }}</option>
                                                    @foreach($field->getOptionsArray() as $option)
                                                        <option value="{{ $option }}" {{ $currentValue == $option ? 'selected' : '' }}>
                                                            {{ $option }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <button type="button" class="btn btn-outline-danger btn-sm m-0" onclick="removeExtraField('{{ $field->field_key }}')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        @else
                                            <div class="input-group">
                                                <input class="form-control"
                                                       type="{{ $field->field_type === 'phone' ? 'tel' : $field->field_type }}"
                                                       name="extra_{{ $field->field_key }}"
                                                       placeholder="{{ $field->field_label }}"
                                                       value="{{ $currentValue }}"
                                                       {{ $field->is_required ? 'required' : '' }}>
                                                <button type="button" class="btn btn-outline-danger btn-sm m-0" onclick="removeExtraField('{{ $field->field_key }}')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        @endif
                                        @error('extra_' . $field->field_key) <div class="text-danger">{{ $message }}</div> @enderror
                                    </div>
                                @endif
                            @endforeach

                            <!-- Dynamic Extra Fields Container for new fields -->
                            <div id="extra-fields-container" class="w-100">
                                <!-- New extra fields will be added here dynamically -->
                            </div>

                            <!-- Add Extra Field Button -->
                            <div class="col-md-4 mt-3">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="add-extra-field">
                                    <i class="fas fa-plus"></i> Adaugă câmp suplimentar
                                </button>
                            </div>
                        </div>
                        <hr/>
                        <div class="participare-options my-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <select name="categorie" class="form-select select-tip-participare">
                                        <option value="">Selectează categoria</option>
                                        @foreach(\App\Models\BadgeCategory::all() as $category)
                                            <option value="{{ $category->name }}" {{ old('categorie', $visitor->categorie) == $category->name ? 'selected' : '' }}>
                                                {{ $category->display_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('categorie') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Actualizați</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="card mt-4">
                <div class="card-header bg-gradient-warning">
                    <h4 class="text-white">Generați un badge customizat</h4>
                </div>
                <div class="card-body">
                    <!-- Quick Badge Generation -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="{{ route('visitors.badge', $visitor->id) }}" target="_blank" class="btn btn-info btn-sm">
                                <i class="fas fa-id-badge me-1"></i>Badge standard
                            </a>
                        </div>
                        <div class="col-md-6 text-end">
                            <img src="{{ $img }}" alt="QR Code" style="max-width: 200px; max-height: 200px;"/>
                        </div>
                    </div>

                    <!-- Custom Badge Form -->
                    <form id="custom-badge-form" method="POST" target="_blank">
                        @csrf

                        <!-- Category Selection -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="category" class="form-label">Categorie badge <span class="text-danger">*</span></label>
                                <select name="category" id="category" class="form-select" required onchange="loadTemplateFields()">
                                    <option value="">Selectează categoria</option>
                                    @foreach($badgeCategories as $category)
                                        <option value="{{ $category->name }}"
                                                {{ $visitor->categorie == $category->name ? 'selected' : '' }}
                                                data-template-id="{{ $category->pdfTemplate ? $category->pdfTemplate->id : '' }}">
                                            {{ $category->display_name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Mod editare</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="advancedMode" onchange="toggleAdvancedMode()">
                                    <label class="form-check-label" for="advancedMode">
                                        Editare avansată (coordonate, dimensiuni)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Dynamic Badge Fields -->
                        <div id="badge-fields-container">
                            <div class="row">
                                <div class="col-12">
                                    <p class="text-muted">Selectați o categorie pentru a vedea câmpurile disponibile.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="button" class="btn btn-warning me-2" onclick="generateCustomBadge(false)">
                                    <i class="fas fa-id-badge me-1"></i>Generează badge custom
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="generateCustomBadge(true)">
                                    <i class="fas fa-id-badge me-1"></i>Generează badge gol
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Available extra fields from database
    const availableFields = @json(\App\Models\VisitorExtraField::getActiveFields());
    const existingFields = @json(array_keys($visitorData ?? []));
    let addedFields = [...existingFields];

    // Badge categories and templates data
    const badgeCategories = @json($badgeCategories);
    const visitor = @json($visitor);
    const defaultTemplate = @json($defaultTemplate);

    document.addEventListener('DOMContentLoaded', function() {
        const addButton = document.getElementById('add-extra-field');
        const container = document.getElementById('extra-fields-container');

        addButton.addEventListener('click', function() {
            showFieldSelector();
        });

        function showFieldSelector() {
            const unusedFields = availableFields.filter(field => !addedFields.includes(field.field_key));

            if (unusedFields.length === 0) {
                alert('Toate câmpurile disponibile au fost adăugate.');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Selectați câmpul de adăugat</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <select class="form-control" id="field-selector">
                                <option value="">Alegeți un câmp...</option>
                                ${unusedFields.map(field => `<option value="${field.field_key}">${field.field_label}</option>`).join('')}
                            </select>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                            <button type="button" class="btn btn-primary" onclick="addSelectedField()">Adaugă</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }

        window.addSelectedField = function() {
            const selector = document.getElementById('field-selector');
            const fieldKey = selector.value;

            if (!fieldKey) {
                alert('Vă rog să selectați un câmp.');
                return;
            }

            const field = availableFields.find(f => f.field_key === fieldKey);
            addExtraField(field);
            addedFields.push(fieldKey);

            // Close modal
            const modal = selector.closest('.modal');
            bootstrap.Modal.getInstance(modal).hide();
        };

        function addExtraField(field) {
            const fieldDiv = document.createElement('div');
            fieldDiv.className = 'col-md-4 mt-3';
            fieldDiv.setAttribute('data-field-key', field.field_key);

            let inputHtml = '';
            if (field.field_type === 'select') {
                const options = field.field_options || [];
                inputHtml = `
                    <select class="form-control" name="extra_${field.field_key}" ${field.is_required ? 'required' : ''}>
                        <option value="">${field.field_label}</option>
                        ${options.map(option => `<option value="${option}">${option}</option>`).join('')}
                    </select>
                `;
            } else {
                const inputType = field.field_type === 'phone' ? 'tel' : field.field_type;
                inputHtml = `
                    <input class="form-control"
                           type="${inputType}"
                           name="extra_${field.field_key}"
                           placeholder="${field.field_label}"
                           ${field.is_required ? 'required' : ''}>
                `;
            }

            fieldDiv.innerHTML = `
                <div class="input-group">
                    ${inputHtml}
                    <button type="button" class="btn btn-outline-danger btn-sm m-0" onclick="removeExtraField('${field.field_key}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            container.appendChild(fieldDiv);
        }

        window.removeExtraField = function(fieldKey) {
            const fieldDiv = document.querySelector(`[data-field-key="${fieldKey}"]`);
            if (fieldDiv) {
                fieldDiv.remove();
                addedFields = addedFields.filter(key => key !== fieldKey);
            }
        };

        // Load default template fields if available
        if (defaultTemplate && defaultTemplate.badge_fields) {
            loadTemplateFields();
        }
    });

    // Custom Badge Functions
    function loadTemplateFields() {
        const categorySelect = document.getElementById('category');
        const selectedCategory = categorySelect.value;
        const container = document.getElementById('badge-fields-container');

        if (!selectedCategory) {
            container.innerHTML = '<div class="row"><div class="col-12"><p class="text-muted">Selectați o categorie pentru a vedea câmpurile disponibile.</p></div></div>';
            return;
        }

        // Find the selected category data
        const category = badgeCategories.find(cat => cat.name === selectedCategory);
        if (!category || !category.pdf_template || !category.pdf_template.badge_fields) {
            container.innerHTML = '<div class="row"><div class="col-12"><p class="text-warning">Nu există câmpuri configurate pentru această categorie.</p></div></div>';
            return;
        }

        const fields = category.pdf_template.badge_fields.filter(field => field.is_active);

        if (fields.length === 0) {
            container.innerHTML = '<div class="row"><div class="col-12"><p class="text-warning">Nu există câmpuri configurate pentru această categorie.</p></div></div>';
            return;
        }

        // Check if advanced mode is enabled
        const isAdvancedMode = document.getElementById('advancedMode').checked;

        if (isAdvancedMode) {
            generateAdvancedFields(fields, container);
        } else {
            generateSimpleFields(fields, container);
        }
    }

    function generateSimpleFields(fields, container) {
            let html = '<div class="alert alert-info text-white"><i class="fas fa-info-circle me-2"></i><strong>Mod simplu:</strong> Editați doar valorile câmpurilor.</div>';
        html += '<div class="row">';

        fields.forEach(field => {
            const defaultValue = getDefaultValueForField(field);
            const fieldLabel = getFieldLabel(field);
            const isQrCode = field.field_name === 'qr_code';

            if (!isQrCode) {
                html += `
                    <div class="col-md-6 mb-3">
                        <label for="field_${field.id}" class="form-label">
                            ${fieldLabel}
                            <small class="text-muted">(${field.font_size}px)</small>
                        </label>
                        <input type="text"
                               class="form-control"
                               id="field_${field.id}"
                               name="field_${field.id}"
                               value="${defaultValue}"
                               placeholder="Introduceți ${fieldLabel.toLowerCase()}">
                    </div>
                `;
            }
        });

        html += '</div>';
        container.innerHTML = html;
    }

    function generateAdvancedFields(fields, container) {
        let html = '<div class="alert alert-info text-white"><i class="fas fa-info-circle me-2"></i><strong>Editare avansată:</strong> Puteți modifica toate proprietățile câmpurilor pentru acest badge.</div>';

        fields.forEach(field => {
            const defaultValue = getDefaultValueForField(field);
            const fieldLabel = getFieldLabel(field);
            const isQrCode = field.field_name === 'qr_code';

            html += `
                <div class="card mb-3">
                    <div class="card-header bg-light py-2">
                        <h6 class="mb-0">${fieldLabel} ${isQrCode ? '(QR Code)' : ''}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            ${!isQrCode ? `
                            <div class="col-md-6 mb-3">
                                <label for="field_${field.id}" class="form-label">Valoare text</label>
                                <input type="text"
                                       class="form-control"
                                       id="field_${field.id}"
                                       name="field_${field.id}"
                                       value="${defaultValue}"
                                       placeholder="Introduceți ${fieldLabel.toLowerCase()}">
                            </div>
                            ` : ''}
                            <div class="col-md-6 mt-2">
                                <label for="alignment_${field.id}" class="form-label">Aliniere</label>
                                <select class="form-select"
                                        id="alignment_${field.id}"
                                        name="alignment_${field.id}">
                                    <option value="L" ${field.alignment === 'L' ? 'selected' : ''}>Stânga (L)</option>
                                    <option value="C" ${field.alignment === 'C' ? 'selected' : ''}>Centru (C)</option>
                                    <option value="R" ${field.alignment === 'R' ? 'selected' : ''}>Dreapta (R)</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="x_coordinate_${field.id}" class="form-label">X (poziție)</label>
                                <input type="number"
                                       class="form-control"
                                       id="x_coordinate_${field.id}"
                                       name="x_coordinate_${field.id}"
                                       value="${field.x_coordinate}"
                                       min="0" max="1000">
                                <small class="text-muted">0 = centrat</small>
                            </div>
                            <div class="col-md-3">
                                <label for="y_coordinate_${field.id}" class="form-label">Y (poziție)</label>
                                <input type="number"
                                       class="form-control"
                                       id="y_coordinate_${field.id}"
                                       name="y_coordinate_${field.id}"
                                       value="${field.y_coordinate}"
                                       min="0" max="1000">
                            </div>
                            <div class="col-md-3">
                                <label for="font_size_${field.id}" class="form-label">${isQrCode ? 'Dimensiune QR' : 'Font size'}</label>
                                <input type="number"
                                       class="form-control"
                                       id="font_size_${field.id}"
                                       name="font_size_${field.id}"
                                       value="${field.font_size}"
                                       min="${isQrCode ? '10' : '6'}"
                                       max="${isQrCode ? '200' : '72'}">
                            </div>
                            ${!isQrCode ? `
                            <div class="col-md-3">
                                <label for="max_width_${field.id}" class="form-label">Lățime max</label>
                                <input type="number"
                                       class="form-control"
                                       id="max_width_${field.id}"
                                       name="max_width_${field.id}"
                                       value="${field.max_width}"
                                       min="0" max="1000">
                                <small class="text-muted">0 = auto</small>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    function toggleAdvancedMode() {
        // Reload the fields with the new mode
        loadTemplateFields();
    }

    function getDefaultValueForField(field) {
        switch (field.field_name) {
            case 'name':
                return `${visitor.prenume || ''} ${visitor.nume || ''}`.trim().toUpperCase();
            case 'first_name':
                return (visitor.prenume || '').toUpperCase();
            case 'last_name':
                return (visitor.nume || '').toUpperCase();
            case 'index_badge':
            case 'index_badge_reversed':
                return (visitor.badge_index || '').toString().toUpperCase();
            case 'region':
                return (visitor.regiune || '').toUpperCase();
            case 'category':
                return (visitor.categorie || '').toUpperCase();
            default:
                // Custom field from JSON
                if (field.json_key && visitor.date) {
                    const jsonData = typeof visitor.date === 'string' ? JSON.parse(visitor.date) : visitor.date;
                    field.json_key = field.json_key.startsWith('extra_') ? field.json_key : 'extra_' + field.json_key;
                    return (jsonData[field.json_key] || '').toString().toUpperCase();
                }
                return '';
        }
    }

    function getFieldLabel(field) {
        const builtInLabels = {
            'name': 'Nume complet',
            'first_name': 'Prenume',
            'last_name': 'Nume',
            'index_badge': 'Index badge',
            'index_badge_reversed': 'Index badge (inversat)',
            'region': 'Regiune',
            'category': 'Categorie',
            'qr_code': 'Cod QR'
        };

        return builtInLabels[field.field_name] || field.json_key || field.field_name;
    }

    function generateCustomBadge(isBlank) {
        const form = document.getElementById('custom-badge-form');
        const category = document.getElementById('category').value;

        if (!category) {
            alert('Vă rog să selectați o categorie pentru badge.');
            return;
        }

        // Set the form action based on badge type
        if (isBlank) {
            form.action = `{{ route('visitors.generateCustomBlankBadge', $visitor->id) }}`;
        } else {
            form.action = `{{ route('visitors.generateCustomBadge', $visitor->id) }}`;
        }

        // Submit the form
        form.submit();
    }
</script>
@endsection