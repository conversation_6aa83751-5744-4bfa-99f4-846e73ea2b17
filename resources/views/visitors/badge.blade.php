@extends('layouts.user_type.auth')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card mt-4">
                <div class="card-header bg-gradient-warning">
                    <h4 class="text-white">Generați un badge customizat</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('visitors.customBadge') }}" method="POST" target="_blank" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="qr" value="skip">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="mb-3">
                                    <label for="nume" class="form-label">Nume</label>
                                    <input type="text" name="nume" class="form-control" id="nume" value="{{ old('nume') }}">
                                    @error('nume') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="mb-3">
                                    <label for="prenume" class="form-label">Prenume</label>
                                    <input type="text" name="prenume" class="form-control" id="prenume" value="{{ old('prenume') }}">
                                    @error('prenume') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="font_size_nume" class="form-label">Font size nume</label>
                                    <input type="number" name="font_size_nume" class="form-control" id="font_size_nume" value="{{ old('font_size_nume', 20) }}">
                                    @error('font_size_nume') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-10">
                                <div class="mb-3">
                                    <label for="nume" class="form-label">Regiune</label>
                                    <input type="text" name="regiune" class="form-control" id="regiune" value="{{ old('regiune') }}">
                                    @error('regiune') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="font_size_regiune" class="form-label">Font size regiune</label>
                                    <input type="number" name="font_size_regiune" class="form-control" id="font_size_regiune" value="{{ old('font_size_regiune', 12) }}">
                                    @error('font_size_regiune') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-10">
                                <div class="mb-3">
                                    <label for="tip" class="form-label">Label</label>
                                    <select name="label" class="form-select" id="tip">
                                        <option value="PARTICIPANT" {{ old('label', 'PARTICIPANT') === 'PARTICIPANT' ? 'selected' : '' }}>PARTICIPANT</option>
                                        <option value="SPEAKER" {{ old('label', 'SPEAKER') === 'SPEAKER' ? 'selected' : '' }}>SPEAKER</option>
                                        <option value="INVITED SPEAKER" {{ old('label', 'INVITED SPEAKER') === 'INVITED SPEAKER' ? 'selected' : '' }}>INVITED SPEAKER</option>
                                        <option value="STAFF" {{ old('label', 'VIZITATOR') === 'STAFF' ? 'selected' : '' }}>STAFF</option>
                                    </select>
                                    @error('tip')
                                    <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="font_size_label" class="form-label">Font size label</label>
                                    <input type="number" name="font_size_label" class="form-control" id="font_size_label" value="{{ old('font_size_label', 16) }}">
                                    @error('font_size_label')
                                    <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-warning">Generați</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
