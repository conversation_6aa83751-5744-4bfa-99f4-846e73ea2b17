@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-gradient-warning">
                        <h4 class="text-white">Scanați un participant</h4>
                    </div>
                    <div class="card-body">
                        <div id="reader"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://unpkg.com/html5-qrcode@2.3.4/html5-qrcode.min.js" type="text/javascript"></script>
    <script>

        let current_scan = false;
        function onScanSuccess(decodedText, decodedResult) {
            if (current_scan) {
                return;
            }
            current_scan = true;

            html5QrcodeScanner.pause();
            console.log(`Scan result: ${decodedText}`);
            let url = '{{ route('visitors.identifyQRPost') }}';
            let data = {
                _token: '{{ csrf_token() }}',
                code: decodedText
            };
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                success: function (response) {
                    if (response.status === 'success') {
                        Swal.fire({
                            title: 'Succes',
                            icon: 'success',
                            confirmButtonText: 'OK',
                            html: response.message,
                        }).then((result) => {
                            if (response.redirect) {
                                window.location.href = response.redirect;
                            } else {
                                html5QrcodeScanner.resume();
                                current_scan = false;
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Eroare',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                html5QrcodeScanner.resume();
                                current_scan = false;
                            }
                        });
                    }
                },
                error: function (error) {
                    Swal.fire({
                        title: 'Eroare',
                        text: 'A apărut o eroare la scanarea codului QR. Vă rugăm să încercați din nou.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            html5QrcodeScanner.resume();
                            current_scan = false;
                        }
                    });
                },
            });
        }

        function onScanFailure(error) {
        }

        let html5QrcodeScanner = new Html5QrcodeScanner(
            "reader", {fps: 10, qrbox: {width: 150, height: 150}});
        html5QrcodeScanner.render(onScanSuccess, onScanFailure);
        // add btn class to the button
        if (document.querySelector('#html5-qrcode-button-camera-permission')) {
            document.querySelector('#html5-qrcode-button-camera-permission').classList.add('btn', 'btn-primary');
            document.querySelector('#html5-qrcode-button-camera-permission').textContent = 'Permiteți accesul la cameră';
        }
        // hide the scan type change button
        if (document.querySelector('#html5-qrcode-anchor-scan-type-change')) {
            document.querySelector('#html5-qrcode-anchor-scan-type-change').classList.add('d-none');
        }

    </script>
@endsection
