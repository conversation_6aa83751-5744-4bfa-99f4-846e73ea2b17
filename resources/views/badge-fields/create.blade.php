@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="card">
            <div class="card-header bg-gradient-primary">
                <h4 class="text-white">Adaugă câmp badge</h4>
                <small class="text-white opacity-8">{{ $template->category->display_name }} - {{ $template->original_name }}</small>
            </div>
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <form action="{{ route('badge-fields.store', $template) }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="field_name" class="form-label">Tip câmp
                            <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('field_name') is-invalid @enderror" id="field_name" name="field_name" required onchange="toggleJsonKey()">
                            <option value="">Selectează tipul câmpului</option>
                            @foreach($builtInFields as $key => $label)
                                <option value="{{ $key }}" {{ old('field_name') == $key ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                            <option value="custom" {{ old('field_name') == 'custom' ? 'selected' : '' }}>
                                Câmp personalizat (din JSON)
                            </option>
                        </select>
                        @error('field_name')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-3" id="customFieldDiv" style="display: none;">
                        <label for="custom_field_name" class="form-label">Nume câmp personalizat</label>
                        <input type="text" class="form-control" id="custom_field_name" placeholder="ex: company, position, etc.">
                        <div class="form-text">
                            Numele câmpului care va fi afișat
                        </div>
                    </div>
                    <div class="mb-3" id="jsonKeyDiv" style="display: none;">
                        <label for="json_key" class="form-label">Cheie JSON
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control @error('json_key') is-invalid @enderror" id="json_key" name="json_key" value="{{ old('json_key') }}" placeholder="ex: company, position, phone">
                        <div class="form-text">
                            Cheia din JSON-ul "date" al vizitatorului
                        </div>
                        @error('json_key')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="x_coordinate" class="form-label">Coordonata X
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control @error('x_coordinate') is-invalid @enderror" id="x_coordinate" name="x_coordinate" value="{{ old('x_coordinate', 0) }}" min="0" max="1000" required>
                                <div class="form-text">
                                    Poziția orizontală (0 = centrat automat)
                                </div>
                                @error('x_coordinate')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="y_coordinate" class="form-label">Coordonata Y
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control @error('y_coordinate') is-invalid @enderror" id="y_coordinate" name="y_coordinate" value="{{ old('y_coordinate', 50) }}" min="0" max="1000" required>
                                <div class="form-text">
                                    Poziția verticală (în pixeli)
                                </div>
                                @error('y_coordinate')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="font_size" class="form-label">Dimensiune font
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control @error('font_size') is-invalid @enderror" id="font_size" name="font_size" value="{{ old('font_size', 12) }}" min="6" max="72" required>
                                <div class="form-text" id="font_size_help">
                                    Dimensiunea fontului (6-72px)
                                </div>
                                @error('font_size')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_width" class="form-label">Lățime maximă</label>
                                <input type="number" class="form-control @error('max_width') is-invalid @enderror" id="max_width" name="max_width" value="{{ old('max_width', 0) }}" min="0" max="1000">
                                <div class="form-text">
                                    Lățimea maximă (0 = lățime completă)
                                </div>
                                @error('max_width')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="alignment" class="form-label">Aliniere
                                    <span class="text-danger">*</span>
                                </label>
                                <select class="form-select @error('alignment') is-invalid @enderror" id="alignment" name="alignment" required>
                                    <option value="L" {{ old('alignment') == 'L' ? 'selected' : '' }}>Stânga (L)</option>
                                    <option value="C" {{ old('alignment', 'C') == 'C' ? 'selected' : '' }}>Centru (C)</option>
                                    <option value="R" {{ old('alignment') == 'R' ? 'selected' : '' }}>Dreapta (R)</option>
                                </select>
                                @error('alignment')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Sfat:</strong> Folosește butonul "Test Badge" din lista template-urilor pentru a verifica pozițiile câmpurilor.
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('badge-fields.index', $template) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Înapoi
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Salvează câmp
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function toggleJsonKey() {
            const fieldName        = document.getElementById('field_name').value;
            const jsonKeyDiv       = document.getElementById('jsonKeyDiv');
            const customFieldDiv   = document.getElementById('customFieldDiv');
            const jsonKeyInput     = document.getElementById('json_key');
            const customFieldInput = document.getElementById('custom_field_name');
            const fontSizeInput    = document.getElementById('font_size');
            const fontSizeHelp     = document.getElementById('font_size_help');
            const maxWidthInput    = document.getElementById('max_width');
            const alignmentSelect  = document.getElementById('alignment');

            if (fieldName === 'custom') {
                jsonKeyDiv.style.display     = 'block';
                customFieldDiv.style.display = 'block';
                jsonKeyInput.required        = true;

                // Update field_name when custom field name changes
                customFieldInput.addEventListener('input', function () {
                    document.getElementById('field_name').value = this.value || 'custom';
                });
            } else {
                jsonKeyDiv.style.display     = 'none';
                customFieldDiv.style.display = 'none';
                jsonKeyInput.required        = false;
                jsonKeyInput.value           = '';
            }

            // Handle QR code field specifics
            if (fieldName === 'qr_code') {
                fontSizeInput.min = '10';
                fontSizeInput.max = '200';
                fontSizeInput.value = '30';
                fontSizeHelp.textContent = 'Dimensiunea QR code-ului (10-200px)';
                maxWidthInput.value = '0';
                maxWidthInput.disabled = true;
                alignmentSelect.value = 'C';
                alignmentSelect.disabled = true;
            } else {
                fontSizeInput.min = '6';
                fontSizeInput.max = '72';
                if (fontSizeInput.value > 72) fontSizeInput.value = '12';
                fontSizeHelp.textContent = 'Dimensiunea fontului (6-72px)';
                maxWidthInput.disabled = false;
                alignmentSelect.disabled = false;
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function () {
            toggleJsonKey();
        });
    </script>
@endsection
